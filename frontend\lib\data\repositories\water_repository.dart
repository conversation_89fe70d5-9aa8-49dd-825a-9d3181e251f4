import '../models/api_response.dart';
import '../models/water_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';

class WaterRepository {
  final ApiClient _apiClient = serviceLocator<ApiClient>();

  Future<ApiResponse<List<WaterSystem>>> getWaterSystems({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/water-systems',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final waterSystems = data.map((json) => WaterSystem.fromJson(json)).toList();
        return ApiResponse.success(waterSystems);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water systems: $e');
    }
  }

  Future<ApiResponse<WaterSystem>> getWaterSystem(String id) async {
    try {
      final response = await _apiClient.get('/water-systems/$id');

      if (response.isSuccess) {
        final waterSystem = WaterSystem.fromJson(response.data['data']);
        return ApiResponse.success(waterSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water system: $e');
    }
  }

  Future<ApiResponse<WaterSystem>> createWaterSystem(WaterSystem waterSystem) async {
    try {
      final response = await _apiClient.post(
        '/water-systems',
        data: waterSystem.toJson(),
      );

      if (response.isSuccess) {
        final createdSystem = WaterSystem.fromJson(response.data['data']);
        return ApiResponse.success(createdSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to create water system: $e');
    }
  }

  Future<ApiResponse<WaterSystem>> updateWaterSystem(String id, WaterSystem waterSystem) async {
    try {
      final response = await _apiClient.put(
        '/water-systems/$id',
        data: waterSystem.toJson(),
      );

      if (response.isSuccess) {
        final updatedSystem = WaterSystem.fromJson(response.data['data']);
        return ApiResponse.success(updatedSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update water system: $e');
    }
  }

  Future<ApiResponse<void>> deleteWaterSystem(String id) async {
    try {
      final response = await _apiClient.delete('/water-systems/$id');

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to delete water system: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getWaterSystemStats({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/water-systems/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(response.data['data']);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water system stats: $e');
    }
  }

  Future<ApiResponse<void>> updateWaterSystemStatus(String id, String status) async {
    try {
      final response = await _apiClient.patch(
        '/water-systems/$id/status',
        data: {'status': status},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update water system status: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterSystemHistory(String id) async {
    try {
      final response = await _apiClient.get('/water-systems/$id/history');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water system history: $e');
    }
  }

  Future<ApiResponse<void>> recordWaterReading(String systemId, Map<String, dynamic> reading) async {
    try {
      final response = await _apiClient.post(
        '/water-systems/$systemId/readings',
        data: reading,
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to record water reading: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterReadings(String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }
      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final response = await _apiClient.get(
        '/water-systems/$systemId/readings',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water readings: $e');
    }
  }

  Future<ApiResponse<void>> triggerWaterSystemAlert(String systemId, String alertType, String message) async {
    try {
      final response = await _apiClient.post(
        '/water-systems/$systemId/alerts',
        data: {
          'alert_type': alertType,
          'message': message,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to trigger water system alert: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterSystemAlerts(String systemId) async {
    try {
      final response = await _apiClient.get('/water-systems/$systemId/alerts');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch water system alerts: $e');
    }
  }
}
