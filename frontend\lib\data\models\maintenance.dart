import 'package:json_annotation/json_annotation.dart';

part 'maintenance.g.dart';

@JsonSerializable()
class MaintenanceIssue {
  final String id;
  final String title;
  final String? description;
  final String departmentId;
  final String? propertyId;
  final String priority; // LOW, MEDIUM, HIGH, URGENT
  final String status; // OPEN, IN_PROGRESS, COMPLETED, RESOLVED, CLOSED, ON_HOLD
  final String startDate;
  final String? expectedEndDate;
  final String? actualEndDate;
  final String reportedBy;
  final String? assignedTo;
  final bool isRecurring;
  final String? recurrenceType; // DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY
  final String? frequency; // Human readable frequency like "Every 3 months", "Weekly", etc.
  final String? nextDueDate;
  final String? remarks;
  final List<String> attachments;
  final String createdAt;
  final String updatedAt;
  final DepartmentInfo? department;
  final PropertyInfo? property;
  final UserInfo? reporter;
  final UserInfo? assignee;

  const MaintenanceIssue({
    required this.id,
    required this.title,
    this.description,
    required this.departmentId,
    this.propertyId,
    required this.priority,
    required this.status,
    required this.startDate,
    this.expectedEndDate,
    this.actualEndDate,
    required this.reportedBy,
    this.assignedTo,
    required this.isRecurring,
    this.recurrenceType,
    this.frequency,
    this.nextDueDate,
    this.remarks,
    required this.attachments,
    required this.createdAt,
    required this.updatedAt,
    this.department,
    this.property,
    this.reporter,
    this.assignee,
  });

  factory MaintenanceIssue.fromJson(Map<String, dynamic> json) => _$MaintenanceIssueFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceIssueToJson(this);

  MaintenanceIssue copyWith({
    String? id,
    String? title,
    String? description,
    String? departmentId,
    String? propertyId,
    String? priority,
    String? status,
    String? startDate,
    String? expectedEndDate,
    String? actualEndDate,
    String? reportedBy,
    String? assignedTo,
    bool? isRecurring,
    String? recurrenceType,
    String? frequency,
    String? nextDueDate,
    String? remarks,
    List<String>? attachments,
    String? createdAt,
    String? updatedAt,
    DepartmentInfo? department,
    PropertyInfo? property,
    UserInfo? reporter,
    UserInfo? assignee,
  }) {
    return MaintenanceIssue(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      propertyId: propertyId ?? this.propertyId,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      expectedEndDate: expectedEndDate ?? this.expectedEndDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      reportedBy: reportedBy ?? this.reportedBy,
      assignedTo: assignedTo ?? this.assignedTo,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrenceType: recurrenceType ?? this.recurrenceType,
      frequency: frequency ?? this.frequency,
      nextDueDate: nextDueDate ?? this.nextDueDate,
      remarks: remarks ?? this.remarks,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      department: department ?? this.department,
      property: property ?? this.property,
      reporter: reporter ?? this.reporter,
      assignee: assignee ?? this.assignee,
    );
  }

  // Helper getters
  bool get isOpen => status == 'OPEN';
  bool get isInProgress => status == 'IN_PROGRESS';
  bool get isCompleted => status == 'COMPLETED';
  bool get isResolved => status == 'RESOLVED';
  bool get isClosed => status == 'CLOSED';
  bool get isOnHold => status == 'ON_HOLD';

  bool get isLowPriority => priority == 'LOW';
  bool get isMediumPriority => priority == 'MEDIUM';
  bool get isHighPriority => priority == 'HIGH';
  bool get isUrgentPriority => priority == 'URGENT';

  bool get hasAttachments => attachments.isNotEmpty;
  bool get isAssigned => assignedTo != null;

  DateTime get startDateTime => DateTime.parse(startDate);
  DateTime? get expectedEndDateTime => expectedEndDate != null ? DateTime.parse(expectedEndDate!) : null;
  DateTime? get actualEndDateTime => actualEndDate != null ? DateTime.parse(actualEndDate!) : null;
  DateTime? get nextDueDateTime => nextDueDate != null ? DateTime.parse(nextDueDate!) : null;
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration? get resolutionTime => actualEndDateTime?.difference(startDateTime);
  int? get resolutionDays => resolutionTime?.inDays;

  String get propertyName => property?.name ?? 'Unknown Property';
  String get departmentName => department?.name ?? 'Unknown Department';
  String get reporterName => reporter?.name ?? 'Unknown Reporter';
  String get assigneeName => assignee?.name ?? 'Unassigned';
}

@JsonSerializable()
class DepartmentInfo {
  final String id;
  final String name;
  final String? description;
  final bool isActive;

  const DepartmentInfo({
    required this.id,
    required this.name,
    this.description,
    required this.isActive,
  });

  factory DepartmentInfo.fromJson(Map<String, dynamic> json) => _$DepartmentInfoFromJson(json);
  Map<String, dynamic> toJson() => _$DepartmentInfoToJson(this);
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String? type;
  final String? address;

  const PropertyInfo({
    required this.id,
    required this.name,
    this.type,
    this.address,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class UserInfo {
  final String id;
  final String name;
  final String? email;

  const UserInfo({
    required this.id,
    required this.name,
    this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class MaintenanceIssueDetail extends MaintenanceIssue {
  final List<MaintenanceComment> comments;
  final List<MaintenanceActivity> activities;
  final List<MaintenanceAttachment> attachmentDetails;

  const MaintenanceIssueDetail({
    required super.id,
    required super.title,
    super.description,
    required super.departmentId,
    super.propertyId,
    required super.priority,
    required super.status,
    required super.startDate,
    super.expectedEndDate,
    super.actualEndDate,
    required super.reportedBy,
    super.assignedTo,
    required super.isRecurring,
    super.recurrenceType,
    super.frequency,
    super.nextDueDate,
    super.remarks,
    required super.attachments,
    required super.createdAt,
    required super.updatedAt,
    super.department,
    super.property,
    super.reporter,
    super.assignee,
    required this.comments,
    required this.activities,
    required this.attachmentDetails,
  });

  factory MaintenanceIssueDetail.fromJson(Map<String, dynamic> json) => _$MaintenanceIssueDetailFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$MaintenanceIssueDetailToJson(this);
}

@JsonSerializable()
class MaintenanceComment {
  final String id;
  final String issueId;
  final String userId;
  final String userName;
  final String comment;
  final String createdAt;

  const MaintenanceComment({
    required this.id,
    required this.issueId,
    required this.userId,
    required this.userName,
    required this.comment,
    required this.createdAt,
  });

  factory MaintenanceComment.fromJson(Map<String, dynamic> json) => _$MaintenanceCommentFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceCommentToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
}

@JsonSerializable()
class MaintenanceActivity {
  final String id;
  final String issueId;
  final String userId;
  final String userName;
  final String action; // CREATED, ASSIGNED, STATUS_CHANGED, RESOLVED, etc.
  final String? oldValue;
  final String? newValue;
  final String createdAt;

  const MaintenanceActivity({
    required this.id,
    required this.issueId,
    required this.userId,
    required this.userName,
    required this.action,
    this.oldValue,
    this.newValue,
    required this.createdAt,
  });

  factory MaintenanceActivity.fromJson(Map<String, dynamic> json) => _$MaintenanceActivityFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceActivityToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
}

@JsonSerializable()
class MaintenanceAttachment {
  final String id;
  final String issueId;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String url;
  final String uploadedBy;
  final String uploadedByName;
  final String createdAt;

  const MaintenanceAttachment({
    required this.id,
    required this.issueId,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.url,
    required this.uploadedBy,
    required this.uploadedByName,
    required this.createdAt,
  });

  factory MaintenanceAttachment.fromJson(Map<String, dynamic> json) => _$MaintenanceAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceAttachmentToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
  String get fileSizeFormatted => _formatFileSize(fileSize);

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

@JsonSerializable()
class MaintenanceStatistics {
  final int totalIssues;
  final int openIssues;
  final int inProgressIssues;
  final int resolvedIssues;
  final int closedIssues;
  final int criticalIssues;
  final int highPriorityIssues;
  final int mediumPriorityIssues;
  final int lowPriorityIssues;
  final double averageResolutionTime; // in hours
  final Map<String, int> issuesByDepartment;
  final Map<String, int> issuesByProperty;
  final List<MaintenanceTrend> trends;

  const MaintenanceStatistics({
    required this.totalIssues,
    required this.openIssues,
    required this.inProgressIssues,
    required this.resolvedIssues,
    required this.closedIssues,
    required this.criticalIssues,
    required this.highPriorityIssues,
    required this.mediumPriorityIssues,
    required this.lowPriorityIssues,
    required this.averageResolutionTime,
    required this.issuesByDepartment,
    required this.issuesByProperty,
    required this.trends,
  });

  factory MaintenanceStatistics.fromJson(Map<String, dynamic> json) => _$MaintenanceStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceStatisticsToJson(this);

  // Helper getters
  double get resolutionRate => totalIssues > 0 ? (resolvedIssues + closedIssues) / totalIssues * 100 : 0;
  double get criticalRate => totalIssues > 0 ? criticalIssues / totalIssues * 100 : 0;
  int get pendingIssues => openIssues + inProgressIssues;
  double get pendingRate => totalIssues > 0 ? pendingIssues / totalIssues * 100 : 0;

  String get averageResolutionTimeFormatted {
    if (averageResolutionTime < 24) {
      return '${averageResolutionTime.toStringAsFixed(1)} hours';
    } else {
      final days = (averageResolutionTime / 24).toStringAsFixed(1);
      return '$days days';
    }
  }
}

@JsonSerializable()
class MaintenanceTrend {
  final String date;
  final int created;
  final int resolved;
  final int pending;

  const MaintenanceTrend({
    required this.date,
    required this.created,
    required this.resolved,
    required this.pending,
  });

  factory MaintenanceTrend.fromJson(Map<String, dynamic> json) => _$MaintenanceTrendFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceTrendToJson(this);

  DateTime get dateTime => DateTime.parse(date);
}
