import '../models/api_response.dart';
import '../models/security_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';

class SecurityRepository {
  final ApiClient _apiClient = serviceLocator<ApiClient>();

  Future<ApiResponse<List<SecuritySystem>>> getSecuritySystems({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/security-systems',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final securitySystems = data.map((json) => SecuritySystem.fromJson(json)).toList();
        return ApiResponse.success(securitySystems);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security systems: $e');
    }
  }

  Future<ApiResponse<SecuritySystem>> getSecuritySystem(String id) async {
    try {
      final response = await _apiClient.get('/security-systems/$id');

      if (response.isSuccess) {
        final securitySystem = SecuritySystem.fromJson(response.data['data']);
        return ApiResponse.success(securitySystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security system: $e');
    }
  }

  Future<ApiResponse<SecuritySystem>> createSecuritySystem(SecuritySystem securitySystem) async {
    try {
      final response = await _apiClient.post(
        '/security-systems',
        data: securitySystem.toJson(),
      );

      if (response.isSuccess) {
        final createdSystem = SecuritySystem.fromJson(response.data['data']);
        return ApiResponse.success(createdSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to create security system: $e');
    }
  }

  Future<ApiResponse<SecuritySystem>> updateSecuritySystem(String id, SecuritySystem securitySystem) async {
    try {
      final response = await _apiClient.put(
        '/security-systems/$id',
        data: securitySystem.toJson(),
      );

      if (response.isSuccess) {
        final updatedSystem = SecuritySystem.fromJson(response.data['data']);
        return ApiResponse.success(updatedSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update security system: $e');
    }
  }

  Future<ApiResponse<void>> deleteSecuritySystem(String id) async {
    try {
      final response = await _apiClient.delete('/security-systems/$id');

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to delete security system: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getSecuritySystemStats({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/security-systems/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(response.data['data']);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security system stats: $e');
    }
  }

  Future<ApiResponse<void>> updateSecuritySystemStatus(String id, String status) async {
    try {
      final response = await _apiClient.patch(
        '/security-systems/$id/status',
        data: {'status': status},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update security system status: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSecuritySystemHistory(String id) async {
    try {
      final response = await _apiClient.get('/security-systems/$id/history');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security system history: $e');
    }
  }

  Future<ApiResponse<void>> armSecuritySystem(String systemId) async {
    try {
      final response = await _apiClient.post(
        '/security-systems/$systemId/arm',
        data: {},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to arm security system: $e');
    }
  }

  Future<ApiResponse<void>> disarmSecuritySystem(String systemId) async {
    try {
      final response = await _apiClient.post(
        '/security-systems/$systemId/disarm',
        data: {},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to disarm security system: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSecurityEvents(String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }
      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final response = await _apiClient.get(
        '/security-systems/$systemId/events',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security events: $e');
    }
  }

  Future<ApiResponse<void>> triggerSecurityAlert(String systemId, String alertType, String message) async {
    try {
      final response = await _apiClient.post(
        '/security-systems/$systemId/alerts',
        data: {
          'alert_type': alertType,
          'message': message,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to trigger security alert: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSecurityAlerts(String systemId) async {
    try {
      final response = await _apiClient.get('/security-systems/$systemId/alerts');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch security alerts: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getCameraFeeds(String systemId) async {
    try {
      final response = await _apiClient.get('/security-systems/$systemId/cameras');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch camera feeds: $e');
    }
  }

  Future<ApiResponse<void>> controlCamera(String systemId, String cameraId, String action) async {
    try {
      final response = await _apiClient.post(
        '/security-systems/$systemId/cameras/$cameraId/control',
        data: {'action': action},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to control camera: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getAccessLogs(String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }
      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final response = await _apiClient.get(
        '/security-systems/$systemId/access-logs',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch access logs: $e');
    }
  }
}
