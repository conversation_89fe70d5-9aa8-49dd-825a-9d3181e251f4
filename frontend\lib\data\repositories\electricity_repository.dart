import '../models/api_response.dart';
import '../models/electricity_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';

class ElectricityRepository {
  final ApiClient _apiClient = serviceLocator<ApiClient>();

  Future<ApiResponse<List<ElectricitySystem>>> getElectricitySystems({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/electricity-systems',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final electricitySystems = data.map((json) => ElectricitySystem.fromJson(json)).toList();
        return ApiResponse.success(electricitySystems);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity systems: $e');
    }
  }

  Future<ApiResponse<ElectricitySystem>> getElectricitySystem(String id) async {
    try {
      final response = await _apiClient.get('/electricity-systems/$id');

      if (response.isSuccess) {
        final electricitySystem = ElectricitySystem.fromJson(response.data['data']);
        return ApiResponse.success(electricitySystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity system: $e');
    }
  }

  Future<ApiResponse<ElectricitySystem>> createElectricitySystem(ElectricitySystem electricitySystem) async {
    try {
      final response = await _apiClient.post(
        '/electricity-systems',
        data: electricitySystem.toJson(),
      );

      if (response.isSuccess) {
        final createdSystem = ElectricitySystem.fromJson(response.data['data']);
        return ApiResponse.success(createdSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to create electricity system: $e');
    }
  }

  Future<ApiResponse<ElectricitySystem>> updateElectricitySystem(String id, ElectricitySystem electricitySystem) async {
    try {
      final response = await _apiClient.put(
        '/electricity-systems/$id',
        data: electricitySystem.toJson(),
      );

      if (response.isSuccess) {
        final updatedSystem = ElectricitySystem.fromJson(response.data['data']);
        return ApiResponse.success(updatedSystem);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update electricity system: $e');
    }
  }

  Future<ApiResponse<void>> deleteElectricitySystem(String id) async {
    try {
      final response = await _apiClient.delete('/electricity-systems/$id');

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to delete electricity system: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getElectricitySystemStats({String? propertyId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) {
        queryParams['property_id'] = propertyId;
      }

      final response = await _apiClient.get(
        '/electricity-systems/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(response.data['data']);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity system stats: $e');
    }
  }

  Future<ApiResponse<void>> updateElectricitySystemStatus(String id, String status) async {
    try {
      final response = await _apiClient.patch(
        '/electricity-systems/$id/status',
        data: {'status': status},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to update electricity system status: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getElectricitySystemHistory(String id) async {
    try {
      final response = await _apiClient.get('/electricity-systems/$id/history');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity system history: $e');
    }
  }

  Future<ApiResponse<void>> recordElectricityReading(String systemId, Map<String, dynamic> reading) async {
    try {
      final response = await _apiClient.post(
        '/electricity-systems/$systemId/readings',
        data: reading,
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to record electricity reading: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getElectricityReadings(String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }
      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final response = await _apiClient.get(
        '/electricity-systems/$systemId/readings',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity readings: $e');
    }
  }

  Future<ApiResponse<void>> controlGenerator(String systemId, String action) async {
    try {
      final response = await _apiClient.post(
        '/electricity-systems/$systemId/generator/control',
        data: {'action': action},
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to control generator: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getGeneratorStatus(String systemId) async {
    try {
      final response = await _apiClient.get('/electricity-systems/$systemId/generator/status');

      if (response.isSuccess) {
        return ApiResponse.success(response.data['data']);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch generator status: $e');
    }
  }

  Future<ApiResponse<void>> triggerElectricitySystemAlert(String systemId, String alertType, String message) async {
    try {
      final response = await _apiClient.post(
        '/electricity-systems/$systemId/alerts',
        data: {
          'alert_type': alertType,
          'message': message,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to trigger electricity system alert: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getElectricitySystemAlerts(String systemId) async {
    try {
      final response = await _apiClient.get('/electricity-systems/$systemId/alerts');

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch electricity system alerts: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getPowerConsumption(String systemId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _apiClient.get(
        '/electricity-systems/$systemId/consumption',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(response.data['data']);
      } else {
        return ApiResponse.error(response.message);
      }
    } catch (e) {
      return ApiResponse.error('Failed to fetch power consumption: $e');
    }
  }
}
