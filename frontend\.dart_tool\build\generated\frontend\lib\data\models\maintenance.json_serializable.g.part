// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MaintenanceIssue _$MaintenanceIssueFromJson(Map<String, dynamic> json) =>
    MaintenanceIssue(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      departmentId: json['departmentId'] as String,
      propertyId: json['propertyId'] as String?,
      priority: json['priority'] as String,
      status: json['status'] as String,
      startDate: json['startDate'] as String,
      expectedEndDate: json['expectedEndDate'] as String?,
      actualEndDate: json['actualEndDate'] as String?,
      reportedBy: json['reportedBy'] as String,
      assignedTo: json['assignedTo'] as String?,
      isRecurring: json['isRecurring'] as bool,
      recurrenceType: json['recurrenceType'] as String?,
      frequency: json['frequency'] as String?,
      nextDueDate: json['nextDueDate'] as String?,
      remarks: json['remarks'] as String?,
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      department: json['department'] == null
          ? null
          : DepartmentInfo.fromJson(json['department'] as Map<String, dynamic>),
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
      reporter: json['reporter'] == null
          ? null
          : UserInfo.fromJson(json['reporter'] as Map<String, dynamic>),
      assignee: json['assignee'] == null
          ? null
          : UserInfo.fromJson(json['assignee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MaintenanceIssueToJson(MaintenanceIssue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'departmentId': instance.departmentId,
      'propertyId': instance.propertyId,
      'priority': instance.priority,
      'status': instance.status,
      'startDate': instance.startDate,
      'expectedEndDate': instance.expectedEndDate,
      'actualEndDate': instance.actualEndDate,
      'reportedBy': instance.reportedBy,
      'assignedTo': instance.assignedTo,
      'isRecurring': instance.isRecurring,
      'recurrenceType': instance.recurrenceType,
      'frequency': instance.frequency,
      'nextDueDate': instance.nextDueDate,
      'remarks': instance.remarks,
      'attachments': instance.attachments,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'department': instance.department,
      'property': instance.property,
      'reporter': instance.reporter,
      'assignee': instance.assignee,
    };

DepartmentInfo _$DepartmentInfoFromJson(Map<String, dynamic> json) =>
    DepartmentInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$DepartmentInfoToJson(DepartmentInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'isActive': instance.isActive,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String?,
      address: json['address'] as String?,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
    };

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
    };

MaintenanceIssueDetail _$MaintenanceIssueDetailFromJson(
        Map<String, dynamic> json) =>
    MaintenanceIssueDetail(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      departmentId: json['departmentId'] as String,
      propertyId: json['propertyId'] as String?,
      priority: json['priority'] as String,
      status: json['status'] as String,
      startDate: json['startDate'] as String,
      expectedEndDate: json['expectedEndDate'] as String?,
      actualEndDate: json['actualEndDate'] as String?,
      reportedBy: json['reportedBy'] as String,
      assignedTo: json['assignedTo'] as String?,
      isRecurring: json['isRecurring'] as bool,
      recurrenceType: json['recurrenceType'] as String?,
      frequency: json['frequency'] as String?,
      nextDueDate: json['nextDueDate'] as String?,
      remarks: json['remarks'] as String?,
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      department: json['department'] == null
          ? null
          : DepartmentInfo.fromJson(json['department'] as Map<String, dynamic>),
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
      reporter: json['reporter'] == null
          ? null
          : UserInfo.fromJson(json['reporter'] as Map<String, dynamic>),
      assignee: json['assignee'] == null
          ? null
          : UserInfo.fromJson(json['assignee'] as Map<String, dynamic>),
      comments: (json['comments'] as List<dynamic>)
          .map((e) => MaintenanceComment.fromJson(e as Map<String, dynamic>))
          .toList(),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => MaintenanceActivity.fromJson(e as Map<String, dynamic>))
          .toList(),
      attachmentDetails: (json['attachmentDetails'] as List<dynamic>)
          .map((e) => MaintenanceAttachment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MaintenanceIssueDetailToJson(
        MaintenanceIssueDetail instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'departmentId': instance.departmentId,
      'propertyId': instance.propertyId,
      'priority': instance.priority,
      'status': instance.status,
      'startDate': instance.startDate,
      'expectedEndDate': instance.expectedEndDate,
      'actualEndDate': instance.actualEndDate,
      'reportedBy': instance.reportedBy,
      'assignedTo': instance.assignedTo,
      'isRecurring': instance.isRecurring,
      'recurrenceType': instance.recurrenceType,
      'frequency': instance.frequency,
      'nextDueDate': instance.nextDueDate,
      'remarks': instance.remarks,
      'attachments': instance.attachments,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'department': instance.department,
      'property': instance.property,
      'reporter': instance.reporter,
      'assignee': instance.assignee,
      'comments': instance.comments,
      'activities': instance.activities,
      'attachmentDetails': instance.attachmentDetails,
    };

MaintenanceComment _$MaintenanceCommentFromJson(Map<String, dynamic> json) =>
    MaintenanceComment(
      id: json['id'] as String,
      issueId: json['issueId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      comment: json['comment'] as String,
      createdAt: json['createdAt'] as String,
    );

Map<String, dynamic> _$MaintenanceCommentToJson(MaintenanceComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'issueId': instance.issueId,
      'userId': instance.userId,
      'userName': instance.userName,
      'comment': instance.comment,
      'createdAt': instance.createdAt,
    };

MaintenanceActivity _$MaintenanceActivityFromJson(Map<String, dynamic> json) =>
    MaintenanceActivity(
      id: json['id'] as String,
      issueId: json['issueId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      action: json['action'] as String,
      oldValue: json['oldValue'] as String?,
      newValue: json['newValue'] as String?,
      createdAt: json['createdAt'] as String,
    );

Map<String, dynamic> _$MaintenanceActivityToJson(
        MaintenanceActivity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'issueId': instance.issueId,
      'userId': instance.userId,
      'userName': instance.userName,
      'action': instance.action,
      'oldValue': instance.oldValue,
      'newValue': instance.newValue,
      'createdAt': instance.createdAt,
    };

MaintenanceAttachment _$MaintenanceAttachmentFromJson(
        Map<String, dynamic> json) =>
    MaintenanceAttachment(
      id: json['id'] as String,
      issueId: json['issueId'] as String,
      fileName: json['fileName'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      url: json['url'] as String,
      uploadedBy: json['uploadedBy'] as String,
      uploadedByName: json['uploadedByName'] as String,
      createdAt: json['createdAt'] as String,
    );

Map<String, dynamic> _$MaintenanceAttachmentToJson(
        MaintenanceAttachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'issueId': instance.issueId,
      'fileName': instance.fileName,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'url': instance.url,
      'uploadedBy': instance.uploadedBy,
      'uploadedByName': instance.uploadedByName,
      'createdAt': instance.createdAt,
    };

MaintenanceStatistics _$MaintenanceStatisticsFromJson(
        Map<String, dynamic> json) =>
    MaintenanceStatistics(
      totalIssues: (json['totalIssues'] as num).toInt(),
      openIssues: (json['openIssues'] as num).toInt(),
      inProgressIssues: (json['inProgressIssues'] as num).toInt(),
      resolvedIssues: (json['resolvedIssues'] as num).toInt(),
      closedIssues: (json['closedIssues'] as num).toInt(),
      criticalIssues: (json['criticalIssues'] as num).toInt(),
      highPriorityIssues: (json['highPriorityIssues'] as num).toInt(),
      mediumPriorityIssues: (json['mediumPriorityIssues'] as num).toInt(),
      lowPriorityIssues: (json['lowPriorityIssues'] as num).toInt(),
      averageResolutionTime: (json['averageResolutionTime'] as num).toDouble(),
      issuesByDepartment:
          Map<String, int>.from(json['issuesByDepartment'] as Map),
      issuesByProperty: Map<String, int>.from(json['issuesByProperty'] as Map),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => MaintenanceTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MaintenanceStatisticsToJson(
        MaintenanceStatistics instance) =>
    <String, dynamic>{
      'totalIssues': instance.totalIssues,
      'openIssues': instance.openIssues,
      'inProgressIssues': instance.inProgressIssues,
      'resolvedIssues': instance.resolvedIssues,
      'closedIssues': instance.closedIssues,
      'criticalIssues': instance.criticalIssues,
      'highPriorityIssues': instance.highPriorityIssues,
      'mediumPriorityIssues': instance.mediumPriorityIssues,
      'lowPriorityIssues': instance.lowPriorityIssues,
      'averageResolutionTime': instance.averageResolutionTime,
      'issuesByDepartment': instance.issuesByDepartment,
      'issuesByProperty': instance.issuesByProperty,
      'trends': instance.trends,
    };

MaintenanceTrend _$MaintenanceTrendFromJson(Map<String, dynamic> json) =>
    MaintenanceTrend(
      date: json['date'] as String,
      created: (json['created'] as num).toInt(),
      resolved: (json['resolved'] as num).toInt(),
      pending: (json['pending'] as num).toInt(),
    );

Map<String, dynamic> _$MaintenanceTrendToJson(MaintenanceTrend instance) =>
    <String, dynamic>{
      'date': instance.date,
      'created': instance.created,
      'resolved': instance.resolved,
      'pending': instance.pending,
    };
