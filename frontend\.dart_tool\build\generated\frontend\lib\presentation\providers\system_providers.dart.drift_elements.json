{"valid_import": true, "imports": [{"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/data/models/system.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/system_repository.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/core/utils/cache_manager.dart", "transitive": false}, {"uri": "package:frontend/core/utils/connectivity_manager.dart", "transitive": false}], "elements": []}