import 'package:json_annotation/json_annotation.dart';

part 'electricity_system.g.dart';

@JsonSerializable()
class ElectricitySystem {
  final String id;
  final String name;
  final String propertyId;
  final String type; // generator, inverter, solar, main_supply
  final String status; // active, inactive, maintenance, error
  final Map<String, dynamic> specifications;
  final Map<String, dynamic> currentReadings;
  final String location;
  final String? description;
  final bool isActive;
  final String createdAt;
  final String updatedAt;

  const ElectricitySystem({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.type,
    required this.status,
    required this.specifications,
    required this.currentReadings,
    required this.location,
    this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ElectricitySystem.fromJson(Map<String, dynamic> json) => _$ElectricitySystemFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemToJson(this);

  ElectricitySystem copyWith({
    String? id,
    String? name,
    String? propertyId,
    String? type,
    String? status,
    Map<String, dynamic>? specifications,
    Map<String, dynamic>? currentReadings,
    String? location,
    String? description,
    bool? isActive,
    String? createdAt,
    String? updatedAt,
  }) {
    return ElectricitySystem(
      id: id ?? this.id,
      name: name ?? this.name,
      propertyId: propertyId ?? this.propertyId,
      type: type ?? this.type,
      status: status ?? this.status,
      specifications: specifications ?? this.specifications,
      currentReadings: currentReadings ?? this.currentReadings,
      location: location ?? this.location,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  // Electricity system specific getters
  double? get powerOutput => currentReadings['power_output']?.toDouble();
  double? get voltage => currentReadings['voltage']?.toDouble();
  double? get current => currentReadings['current']?.toDouble();
  double? get frequency => currentReadings['frequency']?.toDouble();
  double? get fuelLevel => currentReadings['fuel_level']?.toDouble();
  bool? get generatorStatus => currentReadings['generator_status'];
  double? get batteryLevel => currentReadings['battery_level']?.toDouble();
  double? get loadPercentage => currentReadings['load_percentage']?.toDouble();
  double? get temperature => currentReadings['temperature']?.toDouble();
  int? get runningHours => currentReadings['running_hours'];

  // Specification getters
  double? get ratedPower => specifications['rated_power']?.toDouble();
  double? get maxPower => specifications['max_power']?.toDouble();
  String? get fuelType => specifications['fuel_type'];
  double? get fuelCapacity => specifications['fuel_capacity']?.toDouble();
  double? get efficiency => specifications['efficiency']?.toDouble();

  // Status helpers
  bool get isOperational => status == 'active';
  bool get isInMaintenance => status == 'maintenance';
  bool get hasError => status == 'error';
  bool get isInactive => status == 'inactive';

  // Alert conditions
  bool get isLowFuel => fuelLevel != null && fuelLevel! < 20.0;
  bool get isHighLoad => loadPercentage != null && loadPercentage! > 90.0;
  bool get isLowBattery => batteryLevel != null && batteryLevel! < 20.0;
  bool get isOverheating => temperature != null && temperature! > 80.0;
  bool get isGeneratorRunning => generatorStatus == true;

  // Display helpers
  String get statusDisplay {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Maintenance';
      case 'error':
        return 'Error';
      default:
        return status.toUpperCase();
    }
  }

  String get typeDisplay {
    switch (type) {
      case 'generator':
        return 'Generator';
      case 'inverter':
        return 'Inverter';
      case 'solar':
        return 'Solar Panel';
      case 'main_supply':
        return 'Main Supply';
      default:
        return type.toUpperCase();
    }
  }

  String get powerOutputDisplay {
    if (powerOutput == null) return 'N/A';
    return '${powerOutput!.toStringAsFixed(1)} kW';
  }

  String get voltageDisplay {
    if (voltage == null) return 'N/A';
    return '${voltage!.toStringAsFixed(1)} V';
  }

  String get currentDisplay {
    if (current == null) return 'N/A';
    return '${current!.toStringAsFixed(1)} A';
  }

  String get frequencyDisplay {
    if (frequency == null) return 'N/A';
    return '${frequency!.toStringAsFixed(1)} Hz';
  }

  String get fuelLevelDisplay {
    if (fuelLevel == null) return 'N/A';
    return '${fuelLevel!.toStringAsFixed(1)}%';
  }

  String get generatorStatusDisplay {
    if (generatorStatus == null) return 'N/A';
    return generatorStatus! ? 'Running' : 'Stopped';
  }

  String get batteryLevelDisplay {
    if (batteryLevel == null) return 'N/A';
    return '${batteryLevel!.toStringAsFixed(1)}%';
  }

  String get loadPercentageDisplay {
    if (loadPercentage == null) return 'N/A';
    return '${loadPercentage!.toStringAsFixed(1)}%';
  }

  String get temperatureDisplay {
    if (temperature == null) return 'N/A';
    return '${temperature!.toStringAsFixed(1)}°C';
  }

  String get runningHoursDisplay {
    if (runningHours == null) return 'N/A';
    return '$runningHours hrs';
  }

  String get ratedPowerDisplay {
    if (ratedPower == null) return 'N/A';
    return '${ratedPower!.toStringAsFixed(1)} kW';
  }

  String get fuelCapacityDisplay {
    if (fuelCapacity == null) return 'N/A';
    return '${fuelCapacity!.toStringAsFixed(0)} L';
  }

  String get efficiencyDisplay {
    if (efficiency == null) return 'N/A';
    return '${efficiency!.toStringAsFixed(1)}%';
  }

  // Alert messages
  List<String> get alerts {
    final alerts = <String>[];
    
    if (hasError) {
      alerts.add('System Error');
    }
    
    if (isLowFuel) {
      alerts.add('Low Fuel Level');
    }
    
    if (isHighLoad) {
      alerts.add('High Load');
    }
    
    if (isLowBattery) {
      alerts.add('Low Battery');
    }
    
    if (isOverheating) {
      alerts.add('Overheating');
    }
    
    if (isInMaintenance) {
      alerts.add('Under Maintenance');
    }
    
    return alerts;
  }

  bool get hasAlerts => alerts.isNotEmpty;

  // Factory for creating test/demo data
  factory ElectricitySystem.demo({
    required String id,
    required String name,
    required String propertyId,
    String type = 'generator',
    String status = 'active',
  }) {
    final now = DateTime.now().toIso8601String();
    return ElectricitySystem(
      id: id,
      name: name,
      propertyId: propertyId,
      type: type,
      status: status,
      specifications: {
        'rated_power': 50.0,
        'max_power': 55.0,
        'fuel_type': 'diesel',
        'fuel_capacity': 200.0,
        'efficiency': 85.0,
      },
      currentReadings: {
        'power_output': 35.5,
        'voltage': 230.0,
        'current': 154.3,
        'frequency': 50.0,
        'fuel_level': 75.0,
        'generator_status': true,
        'battery_level': 95.0,
        'load_percentage': 70.0,
        'temperature': 65.0,
        'running_hours': 1250,
      },
      location: 'Generator Room',
      description: 'Main backup generator',
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}
