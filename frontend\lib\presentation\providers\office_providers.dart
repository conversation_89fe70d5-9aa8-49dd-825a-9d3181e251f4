import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/office.dart';
import '../../data/models/attendance.dart';
import '../../data/repositories/office_repository.dart';
import '../../core/services/service_locator.dart';

// Office Repository Provider
final officeRepositoryProvider = Provider<OfficeRepository>((ref) {
  return serviceLocator.officeRepository;
});

// Offices List Provider
final officesProvider = FutureProvider<List<Office>>((ref) async {
  try {
    final repository = ref.read(officeRepositoryProvider);
    final response = await repository.getOffices();

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      return [];
    }
  } catch (e) {
    return [];
  }
});

// Office Detail Provider
final officeDetailProvider = FutureProvider.family<OfficeDetail?, String>((ref, officeId) async {
  try {
    final repository = ref.read(officeRepositoryProvider);
    final response = await repository.getOfficeDetail(officeId);

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      return null;
    }
  } catch (e) {
    return null;
  }
});

// Office Attendance Provider
final officeAttendanceProvider = FutureProvider.family<List<AttendanceRecord>, OfficeAttendanceParams>((ref, params) async {
  try {
    final repository = ref.read(officeRepositoryProvider);
    final response = await repository.getOfficeAttendance(
      officeId: params.officeId,
      date: params.date,
      page: params.page,
      limit: params.limit,
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      return [];
    }
  } catch (e) {
    return [];
  }
});

// Today's Attendance Provider
final todayAttendanceProvider = FutureProvider.family<List<AttendanceRecord>, String>((ref, officeId) async {
  final today = DateTime.now();
  final params = OfficeAttendanceParams(
    officeId: officeId,
    date: '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}',
    page: 1,
    limit: 100,
  );
  
  return ref.watch(officeAttendanceProvider(params).future);
});

// Office Statistics Provider
final officeStatisticsProvider = FutureProvider.family<OfficeStatistics, String>((ref, officeId) async {
  try {
    final office = await ref.watch(officeDetailProvider(officeId).future);
    final todayAttendance = await ref.watch(todayAttendanceProvider(officeId).future);
    
    if (office == null) {
      return const OfficeStatistics(
        totalEmployees: 0,
        presentToday: 0,
        attendanceRate: 0,
        avgCheckInTime: '',
        avgCheckOutTime: '',
      );
    }

    final presentToday = todayAttendance.where((record) => record.checkInTime != null).length;
    final totalEmployees = office.employees.length;
    final attendanceRate = totalEmployees > 0 ? (presentToday / totalEmployees) * 100.0 : 0.0;

    // Calculate average check-in and check-out times
    final checkInTimes = todayAttendance
        .where((record) => record.checkInTime != null)
        .map((record) => record.checkInTime!)
        .toList();
    
    final checkOutTimes = todayAttendance
        .where((record) => record.checkOutTime != null)
        .map((record) => record.checkOutTime!)
        .toList();

    String avgCheckInTime = '';
    String avgCheckOutTime = '';

    if (checkInTimes.isNotEmpty) {
      // Calculate average check-in time (simplified)
      avgCheckInTime = _calculateAverageTime(checkInTimes);
    }

    if (checkOutTimes.isNotEmpty) {
      // Calculate average check-out time (simplified)
      avgCheckOutTime = _calculateAverageTime(checkOutTimes);
    }

    return OfficeStatistics(
      totalEmployees: totalEmployees,
      presentToday: presentToday,
      attendanceRate: attendanceRate,
      avgCheckInTime: avgCheckInTime,
      avgCheckOutTime: avgCheckOutTime,
    );
  } catch (e) {
    return const OfficeStatistics(
      totalEmployees: 0,
      presentToday: 0,
      attendanceRate: 0,
      avgCheckInTime: '',
      avgCheckOutTime: '',
    );
  }
});

// All Offices Statistics Provider
final allOfficesStatisticsProvider = FutureProvider<AllOfficesStatistics>((ref) async {
  try {
    final offices = await ref.watch(officesProvider.future);
    
    int totalOffices = offices.length;
    int totalEmployees = 0;
    int totalPresentToday = 0;
    double totalAttendanceRate = 0;

    for (final office in offices) {
      final stats = await ref.watch(officeStatisticsProvider(office.id).future);
      totalEmployees += stats.totalEmployees;
      totalPresentToday += stats.presentToday;
      totalAttendanceRate += stats.attendanceRate;
    }

    final avgAttendanceRate = totalOffices > 0 ? totalAttendanceRate / totalOffices : 0.0;

    return AllOfficesStatistics(
      totalOffices: totalOffices,
      totalEmployees: totalEmployees,
      totalPresentToday: totalPresentToday,
      avgAttendanceRate: avgAttendanceRate,
    );
  } catch (e) {
    return const AllOfficesStatistics(
      totalOffices: 0,
      totalEmployees: 0,
      totalPresentToday: 0,
      avgAttendanceRate: 0,
    );
  }
});

// Selected Office Provider
final selectedOfficeProvider = StateProvider<String?>((ref) => null);

// Office Attendance Parameters
class OfficeAttendanceParams {
  final String officeId;
  final String date;
  final int page;
  final int limit;

  const OfficeAttendanceParams({
    required this.officeId,
    required this.date,
    this.page = 1,
    this.limit = 50,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OfficeAttendanceParams &&
          runtimeType == other.runtimeType &&
          officeId == other.officeId &&
          date == other.date &&
          page == other.page &&
          limit == other.limit;

  @override
  int get hashCode => officeId.hashCode ^ date.hashCode ^ page.hashCode ^ limit.hashCode;
}

// Office Statistics Model
class OfficeStatistics {
  final int totalEmployees;
  final int presentToday;
  final double attendanceRate;
  final String avgCheckInTime;
  final String avgCheckOutTime;

  const OfficeStatistics({
    required this.totalEmployees,
    required this.presentToday,
    required this.attendanceRate,
    required this.avgCheckInTime,
    required this.avgCheckOutTime,
  });
}

// All Offices Statistics Model
class AllOfficesStatistics {
  final int totalOffices;
  final int totalEmployees;
  final int totalPresentToday;
  final double avgAttendanceRate;

  const AllOfficesStatistics({
    required this.totalOffices,
    required this.totalEmployees,
    required this.totalPresentToday,
    required this.avgAttendanceRate,
  });
}

// Helper function to calculate average time
String _calculateAverageTime(List<String> times) {
  if (times.isEmpty) return '';
  
  try {
    int totalMinutes = 0;
    int validTimes = 0;

    for (final timeStr in times) {
      final time = DateTime.parse('2000-01-01 $timeStr');
      totalMinutes += time.hour * 60 + time.minute;
      validTimes++;
    }

    if (validTimes == 0) return '';

    final avgMinutes = totalMinutes ~/ validTimes;
    final hours = avgMinutes ~/ 60;
    final minutes = avgMinutes % 60;

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  } catch (e) {
    return '';
  }
}

// Submit Attendance Provider
final submitAttendanceProvider = FutureProvider.family<bool, SubmitAttendanceParams>((ref, params) async {
  try {
    final repository = ref.read(officeRepositoryProvider);
    final response = await repository.submitAttendance(
      officeId: params.officeId,
      employeeId: params.employeeId,
      status: params.type,
      checkInTime: params.type == 'check_in' ? params.timestamp : null,
      checkOutTime: params.type == 'check_out' ? params.timestamp : null,
      location: params.location,
    );

    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(officeAttendanceProvider);
      ref.invalidate(todayAttendanceProvider);
      ref.invalidate(officeStatisticsProvider);
      return true;
    } else {
      return false;
    }
  } catch (e) {
    return false;
  }
});

// Submit Attendance Parameters
class SubmitAttendanceParams {
  final String officeId;
  final String employeeId;
  final String type; // 'check_in' or 'check_out'
  final String timestamp;
  final String? location;

  const SubmitAttendanceParams({
    required this.officeId,
    required this.employeeId,
    required this.type,
    required this.timestamp,
    this.location,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubmitAttendanceParams &&
          runtimeType == other.runtimeType &&
          officeId == other.officeId &&
          employeeId == other.employeeId &&
          type == other.type &&
          timestamp == other.timestamp &&
          location == other.location;

  @override
  int get hashCode =>
      officeId.hashCode ^
      employeeId.hashCode ^
      type.hashCode ^
      timestamp.hashCode ^
      location.hashCode;
}
