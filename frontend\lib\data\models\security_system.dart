import 'package:json_annotation/json_annotation.dart';

part 'security_system.g.dart';

@JsonSerializable()
class SecuritySystem {
  final String id;
  final String name;
  final String propertyId;
  final String type; // cctv, alarm, access_control, motion_sensor
  final String status; // active, inactive, maintenance, error, armed, disarmed
  final Map<String, dynamic> specifications;
  final Map<String, dynamic> currentReadings;
  final String location;
  final String? description;
  final bool isActive;
  final String createdAt;
  final String updatedAt;

  const SecuritySystem({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.type,
    required this.status,
    required this.specifications,
    required this.currentReadings,
    required this.location,
    this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SecuritySystem.fromJson(Map<String, dynamic> json) => _$SecuritySystemFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemToJson(this);

  SecuritySystem copyWith({
    String? id,
    String? name,
    String? propertyId,
    String? type,
    String? status,
    Map<String, dynamic>? specifications,
    Map<String, dynamic>? currentReadings,
    String? location,
    String? description,
    bool? isActive,
    String? createdAt,
    String? updatedAt,
  }) {
    return SecuritySystem(
      id: id ?? this.id,
      name: name ?? this.name,
      propertyId: propertyId ?? this.propertyId,
      type: type ?? this.type,
      status: status ?? this.status,
      specifications: specifications ?? this.specifications,
      currentReadings: currentReadings ?? this.currentReadings,
      location: location ?? this.location,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  // Security system specific getters
  int? get cameraCount => currentReadings['camera_count'];
  int? get activeCameras => currentReadings['active_cameras'];
  int? get accessPoints => currentReadings['access_points'];
  int? get activeAccessPoints => currentReadings['active_access_points'];
  bool? get alarmStatus => currentReadings['alarm_status'];
  bool? get motionDetected => currentReadings['motion_detected'];
  int? get sensorCount => currentReadings['sensor_count'];
  int? get activeSensors => currentReadings['active_sensors'];
  String? get lastActivity => currentReadings['last_activity'];
  int? get alertCount => currentReadings['alert_count'];

  // Specification getters
  String? get cameraResolution => specifications['camera_resolution'];
  bool? get nightVision => specifications['night_vision'];
  String? get recordingCapacity => specifications['recording_capacity'];
  String? get accessControlType => specifications['access_control_type'];
  int? get maxUsers => specifications['max_users'];

  // Status helpers
  bool get isOperational => status == 'active';
  bool get isInMaintenance => status == 'maintenance';
  bool get hasError => status == 'error';
  bool get isInactive => status == 'inactive';
  bool get isArmed => status == 'armed';
  bool get isDisarmed => status == 'disarmed';

  // Alert conditions
  bool get hasMotionAlert => motionDetected == true;
  bool get hasAlarmTriggered => alarmStatus == true;
  bool get hasCameraOffline => cameraCount != null && activeCameras != null && activeCameras! < cameraCount!;
  bool get hasAccessPointOffline => accessPoints != null && activeAccessPoints != null && activeAccessPoints! < accessPoints!;
  bool get hasSensorOffline => sensorCount != null && activeSensors != null && activeSensors! < sensorCount!;

  // Display helpers
  String get statusDisplay {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Maintenance';
      case 'error':
        return 'Error';
      case 'armed':
        return 'Armed';
      case 'disarmed':
        return 'Disarmed';
      default:
        return status.toUpperCase();
    }
  }

  String get typeDisplay {
    switch (type) {
      case 'cctv':
        return 'CCTV System';
      case 'alarm':
        return 'Alarm System';
      case 'access_control':
        return 'Access Control';
      case 'motion_sensor':
        return 'Motion Sensor';
      default:
        return type.toUpperCase();
    }
  }

  String get cameraStatusDisplay {
    if (cameraCount == null || activeCameras == null) return 'N/A';
    return '$activeCameras/$cameraCount Active';
  }

  String get accessPointStatusDisplay {
    if (accessPoints == null || activeAccessPoints == null) return 'N/A';
    return '$activeAccessPoints/$accessPoints Active';
  }

  String get sensorStatusDisplay {
    if (sensorCount == null || activeSensors == null) return 'N/A';
    return '$activeSensors/$sensorCount Active';
  }

  String get alarmStatusDisplay {
    if (alarmStatus == null) return 'N/A';
    return alarmStatus! ? 'Triggered' : 'Normal';
  }

  String get motionStatusDisplay {
    if (motionDetected == null) return 'N/A';
    return motionDetected! ? 'Motion Detected' : 'No Motion';
  }

  String get lastActivityDisplay {
    if (lastActivity == null) return 'N/A';
    try {
      final dateTime = DateTime.parse(lastActivity!);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return lastActivity!;
    }
  }

  String get alertCountDisplay {
    if (alertCount == null) return '0';
    return alertCount.toString();
  }

  // Alert messages
  List<String> get alerts {
    final alerts = <String>[];
    
    if (hasError) {
      alerts.add('System Error');
    }
    
    if (hasAlarmTriggered) {
      alerts.add('Alarm Triggered');
    }
    
    if (hasMotionAlert) {
      alerts.add('Motion Detected');
    }
    
    if (hasCameraOffline) {
      alerts.add('Camera Offline');
    }
    
    if (hasAccessPointOffline) {
      alerts.add('Access Point Offline');
    }
    
    if (hasSensorOffline) {
      alerts.add('Sensor Offline');
    }
    
    if (isInMaintenance) {
      alerts.add('Under Maintenance');
    }
    
    return alerts;
  }

  bool get hasAlerts => alerts.isNotEmpty;

  // Factory for creating test/demo data
  factory SecuritySystem.demo({
    required String id,
    required String name,
    required String propertyId,
    String type = 'cctv',
    String status = 'active',
  }) {
    final now = DateTime.now().toIso8601String();
    return SecuritySystem(
      id: id,
      name: name,
      propertyId: propertyId,
      type: type,
      status: status,
      specifications: {
        'camera_resolution': '1080p',
        'night_vision': true,
        'recording_capacity': '30 days',
        'access_control_type': 'card_reader',
        'max_users': 100,
      },
      currentReadings: {
        'camera_count': 8,
        'active_cameras': 8,
        'access_points': 4,
        'active_access_points': 4,
        'alarm_status': false,
        'motion_detected': false,
        'sensor_count': 12,
        'active_sensors': 12,
        'last_activity': DateTime.now().subtract(const Duration(minutes: 15)).toIso8601String(),
        'alert_count': 0,
      },
      location: 'Main Building',
      description: 'Primary security monitoring system',
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}
